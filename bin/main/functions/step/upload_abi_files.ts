import { spawnSync } from 'child_process'
import { join } from 'path'
import path from 'path'
import { chdir } from 'process'
import { loadAwsProf } from '../../../common/load_aws_prof'
import { loadEnvVars } from '../../../common/load_env_vars'
import { message, ROOTDIR } from '../../../common/utils'

// スクリプト名の取得
const scriptName = path.basename(__filename)

/**
 * ABIファイルのバックアップを行う
 * @param network 環境名
 * @param zoneId ゾーンID
 */
export async function uploadABIfiles(network: string, zoneId?: string) {
  try {
    // ローカル環境向けのリリースの場合、処理をスキップする
    if (network.includes('local')) {
      message('info', `Skipping ${scriptName} for local environments.`)
      process.exit(0)
    }

    // AWS_PROFILEの設定
    const profile = loadAwsProf(network)

    // 環境変数の読み出しとエクスポート
    loadEnvVars(profile, zoneId)

    const targetZoneId = process.env.ZONE_ID
    if (!targetZoneId) {
      message('err', 'ZONE_ID environment variable is not set.')
      process.exit(1)
    }

    // アップロード先の設定
    const deploymentDir = join(ROOTDIR, 'deployments', network)
    chdir(deploymentDir)

    const backupS3 = process.env.BACKUP_S3
    if (!backupS3) {
      message('err', 'BACKUP_S3 environment variable is not set.')
      process.exit(1)
    }

    message('info', `Upload ABI to s3://${backupS3}/${targetZoneId}`)

    // アップロードファイルの定義
    let files: string[] = []
    switch (targetZoneId) {
      case '3000':
        files = [
          'Token.json',
          'Account.json',
          'Validator.json',
          'Provider.json',
          'Issuer.json',
          'IBCToken.json',
          'FinancialCheck.json',
          'BusinessZoneAccount.json',
          'FinancialZoneAccount.json',
          'JPYTokenTransferBridge.json',
          'AccountSyncBridge.json',
          'BalanceSyncBridge.json',
        ]
        break
      default:
        if (/^3\d{3}$/.test(targetZoneId)) {
          files = [
            'Token.json',
            'Account.json',
            'Validator.json',
            'Provider.json',
            'IBCToken.json',
            'RenewableEnergyToken.json',
            'JPYTokenTransferBridge.json',
            'AccountSyncBridge.json',
            'BalanceSyncBridge.json',
          ]
        } else {
          message('err', `Invalid ZONE_ID: ${targetZoneId}. Please set ZONE_ID to 3000 ~ 3999.`)
          process.exit(1)
        }
    }

    files.forEach((file) => {
      spawnSync('aws', ['s3', 'cp', file, `s3://${backupS3}/${targetZoneId}/`], { stdio: 'inherit' })
    })

    message('success', `Files have been successfully copied to s3://${backupS3}/${targetZoneId}/`)

    // List uploaded files
    spawnSync('aws', ['s3', 'ls', `s3://${backupS3}/${targetZoneId}/`], { stdio: 'inherit' })
    message('success', 'Verification of the backed up files is complete.')
  } catch (err) {
    message('err', `An unexpected error occurred: ${(err as Error).message}`)
    process.exit(1)
  }
}

// 直接実行用
if (require.main === module) {
  // 引数チェック
  const network = process.argv[2]
  const zoneId = process.argv[3]
  if (!network) {
    message('err', 'Please specify the NETWORK as the first argument.')
    console.log(`npx ts-node bin/main/functions/step/${scriptName} [NETWORK name] [ZONE_ID ※任意]`)
    process.exit(1)
  }

  uploadABIfiles(network, zoneId).catch((err) => {
    console.error('[ERROR]', err)
    process.exit(1)
  })
}
