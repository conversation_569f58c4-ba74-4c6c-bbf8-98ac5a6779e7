import { spawnSync } from 'child_process'
import path from 'path'
import { chdir } from 'process'
import { loadAwsProf } from '../../../common/load_aws_prof'
import { loadEnvVars } from '../../../common/load_env_vars'
import { message, ROOTDIR } from '../../../common/utils'

// スクリプト名の取得
const scriptName = path.basename(__filename)

// 定数定義
const ZONE_ID_MAIN = '3000'
const ZONE_ID_PATTERN = /^3\d{3}$/
const MIN_ZONE_ID = 3000
const MAX_ZONE_ID = 3999

// ファイルリストの定義
const FILE_LISTS = {
  main: [
    'Token.json',
    'Account.json',
    'Validator.json',
    'Provider.json',
    'Issuer.json',
    'IBCToken.json',
    'FinancialCheck.json',
    'BusinessZoneAccount.json',
    'FinancialZoneAccount.json',
    'JPYTokenTransferBridge.json',
    'AccountSyncBridge.json',
    'BalanceSyncBridge.json',
  ],
  renewable: [
    'Token.json',
    'Account.json',
    'Validator.json',
    'Provider.json',
    'IBCToken.json',
    'RenewableEnergyToken.json',
    'JPYTokenTransferBridge.json',
    'AccountSyncBridge.json',
    'BalanceSyncBridge.json',
  ],
} as const

type FileListType = keyof typeof FILE_LISTS

/**
 * Zone IDに基づいてファイルリストを取得する
 * @param zoneId ゾーンID
 * @returns ファイルリスト
 */
function getFileListByZoneId(zoneId: string): string[] {
  const zoneIdNum = parseInt(zoneId, 10)

  if (zoneId === ZONE_ID_MAIN) {
    return FILE_LISTS.main
  }

  if (ZONE_ID_PATTERN.test(zoneId) && zoneIdNum >= MIN_ZONE_ID && zoneIdNum <= MAX_ZONE_ID) {
    return FILE_LISTS.renewable
  }

  throw new Error(`Invalid ZONE_ID: ${zoneId}. Please set ZONE_ID to ${MIN_ZONE_ID} ~ ${MAX_ZONE_ID}.`)
}

/**
 * AWS S3にファイルをアップロードする
 * @param files ファイルリスト
 * @param backupS3 S3バケット名
 * @param targetZoneId ゾーンID
 * @returns アップロード成功の可否
 */
function uploadFilesToS3(files: string[], backupS3: string, targetZoneId: string): boolean {
  let allSuccess = true

  for (const file of files) {
    const result = spawnSync('aws', ['s3', 'cp', file, `s3://${backupS3}/${targetZoneId}/`], {
      stdio: 'inherit'
    })

    if (result.status !== 0) {
      message('err', `Failed to upload ${file}`)
      allSuccess = false
    }
  }

  return allSuccess
}

/**
 * ABIファイルのアップロードを行う
 * @param network 環境名
 * @param zoneId ゾーンID
 * @returns 処理結果
 */
export async function uploadABIfiles(network: string, zoneId?: string): Promise<{ success: boolean; message?: string }> {
  try {
    // ローカル環境向けのリリースの場合、処理をスキップする
    if (network.includes('local')) {
      message('info', `Skipping ${scriptName} for local environments.`)
      return { success: true, message: 'Skipped for local environment' }
    }

    // AWS_PROFILEの設定
    const profile = loadAwsProf(network)

    // 環境変数の読み出しとエクスポート
    loadEnvVars(profile, zoneId)

    const targetZoneId = process.env.ZONE_ID
    if (!targetZoneId) {
      throw new Error('ZONE_ID environment variable is not set.')
    }

    const backupS3 = process.env.BACKUP_S3
    if (!backupS3) {
      throw new Error('BACKUP_S3 environment variable is not set.')
    }

    // アップロード先の設定
    const deploymentDir = path.join(ROOTDIR, 'deployments', network)
    chdir(deploymentDir)

    message('info', `Upload ABI to s3://${backupS3}/${targetZoneId}`)

    // アップロードファイルの取得
    const files = getFileListByZoneId(targetZoneId)

    // ファイルのアップロード
    const uploadSuccess = uploadFilesToS3(files, backupS3, targetZoneId)

    if (!uploadSuccess) {
      throw new Error('Some files failed to upload')
    }

    message('success', `Files have been successfully copied to s3://${backupS3}/${targetZoneId}/`)

    // アップロードされたファイルの確認
    const listResult = spawnSync('aws', ['s3', 'ls', `s3://${backupS3}/${targetZoneId}/`], {
      stdio: 'inherit'
    })

    if (listResult.status === 0) {
      message('success', 'Verification of the backed up files is complete.')
      return { success: true }
    } else {
      message('err', 'Failed to verify uploaded files')
      return { success: false, message: 'Verification failed' }
    }
  } catch (err) {
    const errorMessage = `An unexpected error occurred: ${(err as Error).message}`
    message('err', errorMessage)
    return { success: false, message: errorMessage }
  }
}

// 直接実行用
if (require.main === module) {
  // 引数チェック
  const network = process.argv[2]
  const zoneId = process.argv[3]

  if (!network) {
    message('err', 'Please specify the NETWORK as the first argument.')
    console.log(`npx ts-node bin/main/functions/step/${scriptName} [NETWORK name] [ZONE_ID ※任意]`)
    process.exit(1)
  }

  uploadABIfiles(network, zoneId)
    .then((result) => {
      if (!result.success) {
        console.error('[ERROR]', result.message)
        process.exit(1)
      }
      process.exit(0)
    })
    .catch((err) => {
      console.error('[ERROR]', err)
      process.exit(1)
    })
}
